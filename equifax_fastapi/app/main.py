from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse
import base64
import time
from typing import Dict, Any
import io

from .routes import yolo, ocr
from .services.file_handler import FileHandler
from .services.yolo_service import YoloService
from .services.ocr_service import OCRService
from .core.config import config
from .core.logger import app_logger
from .models.schemas import ProcessFileRequest, ProcessFileResponse, FileStatus

# Initialize FastAPI app
app = FastAPI(
    title="Equifax Card Redaction API",
    description="FastAPI application for card detection and redaction using YOLO and OCR",
    version="1.0.0",
    debug=config.debug
)

# Include routers
app.include_router(yolo.router, prefix="/api/v1", tags=["YOLO Detection"])
app.include_router(ocr.router, prefix="/api/v1", tags=["OCR Processing"])

# Initialize services
file_handler = FileHandler()
yolo_service = YoloService()
ocr_service = OCRService()

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Equifax Card Redaction API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "services": {
            "yolo": "loaded",
            "ocr": "loaded",
            "file_handler": "ready"
        }
    }

@app.post("/wrapper", response_model=Dict[str, Any])
async def wrapper_endpoint(request: ProcessFileRequest):
    """
    Main wrapper endpoint that replicates the original wrapper.py functionality
    Processes file through YOLO -> OCR pipeline based on conditions
    """
    try:
        start_time = time.time()
        
        # Decode base64 file data
        file_data = base64.b64decode(request.file_data)
        
        # Validate file type
        validation_result = file_handler.validate_file_type(request.file_type)
        if not validation_result.is_valid:
            app_logger.log_api_call(
                filename="Invalid file",
                file_type=request.file_type,
                is_card="No",
                status="File read error",
                api="File read error"
            )
            return {
                "img-str": request.file_data,
                "img-type": request.file_type,
                "status": "File read error"
            }
        
        # Convert file to image arrays
        conversion_result = file_handler.convert_file_to_images(file_data, request.file_type)
        if not conversion_result.success:
            app_logger.log_api_call(
                filename=conversion_result.file_id,
                file_type=request.file_type,
                is_card="No",
                status="File read error",
                api="File read error"
            )
            return {
                "img-str": request.file_data,
                "img-type": request.file_type,
                "status": "File read error"
            }
        
        # Process each image through the pipeline
        processed_images = []
        overall_status = FileStatus.UNMASKED
        global_mask_count = 0
        global_nocard_count = 0
        
        for page_num, image_array in enumerate(conversion_result.images):
            # Step 1: YOLO Detection
            yolo_response = yolo_service.detect_cards(
                image_array.data,
                conversion_result.file_id,
                conversion_result.file_id,
                request.file_type
            )
            
            # Log YOLO result
            app_logger.log_api_call(
                filename=conversion_result.file_id,
                file_type=request.file_type,
                is_card="Yes" if yolo_response.status != FileStatus.NOT_A_CARD else "No",
                status=yolo_response.status.value,
                api="yolo",
                page_no=page_num if len(conversion_result.images) > 1 else None
            )
            
            current_image = image_array.data
            current_status = yolo_response.status
            
            # Step 2: Check if OCR should be triggered (simplified condition)
            if yolo_response.activation_status == 1:
                # Get coordinates for OCR
                ocr_coords = yolo_service.get_detection_coordinates_for_ocr(yolo_response.detections)
                
                if ocr_coords:  # Only run OCR if we have card coordinates
                    # Run OCR processing
                    masked_image, ocr_response = ocr_service.process_image_with_ocr(
                        current_image,
                        ocr_coords,
                        conversion_result.file_id,
                        conversion_result.file_id,
                        request.file_type
                    )
                    
                    # Log OCR result
                    app_logger.log_api_call(
                        filename=conversion_result.file_id,
                        file_type=request.file_type,
                        is_card="Yes",
                        status=ocr_response.status.value,
                        api="OCR",
                        page_no=page_num if len(conversion_result.images) > 1 else None
                    )
                    
                    current_image = masked_image
                    current_status = ocr_response.status
            
            # Update counters and overall status
            if current_status == FileStatus.MASKED:
                global_mask_count += 1
                overall_status = FileStatus.MASKED
            elif current_status == FileStatus.NOT_A_CARD:
                global_nocard_count += 1
            
            processed_images.append(current_image)
        
        # Determine final status based on original logic
        if global_nocard_count == len(conversion_result.images):
            final_status = FileStatus.NOT_A_CARD
        elif global_mask_count > 0:
            final_status = FileStatus.MASKED
        else:
            final_status = FileStatus.UNMASKED
        
        # Check for timeout
        processing_time = time.time() - start_time
        if processing_time > config.api_timeout:
            final_status = FileStatus.INTERNAL_ERROR
        
        # Combine processed images back to original format
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, request.file_type
        )
        
        if not success:
            app_logger.log_error(f"Error combining processed images: {result_data}")
            return {
                "img-str": request.file_data,
                "img-type": request.file_type,
                "status": "Internal error"
            }
        
        # Encode result as base64
        if isinstance(result_data, list):
            # For DOCX with multiple images
            result_base64 = [base64.b64encode(img_bytes).decode('utf-8') for img_bytes in result_data]
        else:
            result_base64 = base64.b64encode(result_data).decode('utf-8')
        
        return {
            "img-str": result_base64,
            "img-type": request.file_type,
            "status": final_status.value
        }
        
    except Exception as e:
        app_logger.log_error(f"Error in wrapper endpoint", e)
        return {
            "img-str": request.file_data,
            "img-type": request.file_type,
            "status": "Internal error"
        }

@app.post("/wrapper/stream")
async def wrapper_stream_endpoint(request: ProcessFileRequest):
    """
    Wrapper endpoint with streaming response
    Returns processed file as streaming response
    """
    try:
        # Decode and validate
        file_data = base64.b64decode(request.file_data)
        validation_result = file_handler.validate_file_type(request.file_type)
        if not validation_result.is_valid:
            raise HTTPException(status_code=400, detail=validation_result.error_message)
        
        # Convert and process
        conversion_result = file_handler.convert_file_to_images(file_data, request.file_type)
        if not conversion_result.success:
            raise HTTPException(status_code=400, detail=conversion_result.error_message)
        
        # Process through pipeline
        processed_images = []
        for image_array in conversion_result.images:
            # YOLO detection
            yolo_response = yolo_service.detect_cards(
                image_array.data,
                conversion_result.file_id,
                conversion_result.file_id,
                request.file_type
            )
            
            current_image = image_array.data
            
            # OCR if needed
            if yolo_response.activation_status == 1:
                ocr_coords = yolo_service.get_detection_coordinates_for_ocr(yolo_response.detections)
                if ocr_coords:
                    masked_image, _ = ocr_service.process_image_with_ocr(
                        current_image,
                        ocr_coords,
                        conversion_result.file_id,
                        conversion_result.file_id,
                        request.file_type
                    )
                    current_image = masked_image
            
            processed_images.append(current_image)
        
        # Combine and stream
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, request.file_type
        )
        
        if not success:
            raise HTTPException(status_code=500, detail=f"Error processing: {result_data}")
        
        if isinstance(result_data, bytes):
            return StreamingResponse(
                io.BytesIO(result_data),
                media_type=content_type,
                headers={"Content-Disposition": f"attachment; filename=processed.{request.file_type}"}
            )
        else:
            raise HTTPException(status_code=500, detail="Unexpected result format")
            
    except HTTPException:
        raise
    except Exception as e:
        app_logger.log_error(f"Error in wrapper streaming endpoint", e)
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=config.api_host,
        port=config.api_port,
        reload=config.debug
    )
