import yaml
import os
from pathlib import Path
from typing import Dict, Any, List

class Config:
    """Configuration loader for the application"""
    
    def __init__(self, config_path: str = None):
        if config_path is None:
            # Default to configs/config.yaml relative to project root
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "configs" / "config.yaml"
        
        self.config_path = Path(config_path)
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r') as file:
                config = yaml.safe_load(file)
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML configuration: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key (supports dot notation)"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    @property
    def debug(self) -> bool:
        return self.get('debug', False)
    
    # YOLO Configuration
    @property
    def yolo_weights(self) -> str:
        return self.get('yolo.weights', 'runs/train/exp/weights/best.pt')
    
    @property
    def yolo_data(self) -> str:
        return self.get('yolo.data', 'card_data/card.yaml')
    
    @property
    def card_threshold(self) -> float:
        return self.get('yolo.card_threshold', 0.6)
    
    @property
    def card_number_threshold(self) -> float:
        return self.get('yolo.card_number_threshold', 0.4)
    
    @property
    def yolo_imgsz(self) -> List[int]:
        return self.get('yolo.imgsz', [640, 640])
    
    @property
    def conf_thres(self) -> float:
        return self.get('yolo.conf_thres', 0.08)
    
    @property
    def iou_thres(self) -> float:
        return self.get('yolo.iou_thres', 0.10)
    
    @property
    def max_det(self) -> int:
        return self.get('yolo.max_det', 1000)
    
    @property
    def device(self) -> str:
        return self.get('yolo.device', '')
    
    # OCR Configuration
    @property
    def ocr_use_angle_cls(self) -> bool:
        return self.get('ocr.use_angle_cls', True)
    
    @property
    def ocr_lang(self) -> str:
        return self.get('ocr.lang', 'en')
    
    @property
    def ocr_show_log(self) -> bool:
        return self.get('ocr.show_log', False)
    
    # Logging Configuration
    @property
    def log_dir(self) -> str:
        return self.get('logging.log_dir', 'logs')
    
    @property
    def log_file(self) -> str:
        return self.get('logging.log_file', 'app.log')
    
    @property
    def log_level(self) -> str:
        return self.get('logging.level', 'INFO')
    
    @property
    def log_format(self) -> str:
        return self.get('logging.format', '%(message)s')
    
    # File Handling Configuration
    @property
    def valid_extensions(self) -> List[str]:
        return self.get('file_handling.valid_extensions', [])
    
    @property
    def temp_dirs(self) -> Dict[str, str]:
        return self.get('file_handling.temp_dirs', {})
    
    @property
    def output_dirs(self) -> Dict[str, str]:
        return self.get('file_handling.output_dirs', {})
    
    @property
    def poppler_path(self) -> str:
        return self.get('file_handling.poppler_path', '')
    
    # API Configuration
    @property
    def api_host(self) -> str:
        return self.get('api.host', '127.0.0.1')
    
    @property
    def api_port(self) -> int:
        return self.get('api.port', 8000)
    
    @property
    def api_timeout(self) -> int:
        return self.get('api.timeout', 600)


# Global config instance
config = Config()
