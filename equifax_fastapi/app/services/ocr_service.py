import numpy as np
import cv2
from typing import List, <PERSON>ple
import gc
from paddleocr import PaddleOCR

from ..core.config import config
from ..core.logger import app_logger
from ..models.schemas import OCRResponse, FileStatus

class OCRService:
    """OCR service using PaddleOCR for card number detection and masking"""
    
    def __init__(self):
        self.ocr = None
        self._load_ocr()
    
    def _load_ocr(self):
        """Load PaddleOCR with configuration"""
        try:
            self.ocr = PaddleOCR(
                use_angle_cls=config.ocr_use_angle_cls,
                lang=config.ocr_lang,
                show_log=config.ocr_show_log
            )
            app_logger.log_info("PaddleOCR loaded successfully")
        except Exception as e:
            app_logger.log_error("Failed to load PaddleOCR", e)
            raise
    
    def process_image_with_ocr(self, image_array: np.ndarray, card_coordinates: List[List[float]], 
                              filename: str, file_id: str, file_extension: str) -> <PERSON><PERSON>[np.ndar<PERSON>, OCRResponse]:
        """Process image with OCR to detect and mask card numbers"""
        try:
            # Convert image array to format expected by OCR
            if image_array.dtype != np.uint8:
                image_array = (image_array * 255).astype(np.uint8)
            
            # Ensure image is in BGR format for OpenCV operations
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                # Assume input is RGB, convert to BGR for OpenCV
                card_img = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            else:
                card_img = image_array.copy()
            
            # Process each card region
            masked_image, status = self._ocr_cv2(card_img, card_coordinates)
            
            # Clean up memory
            gc.collect()
            
            response = OCRResponse(
                filename=filename,
                img_type=file_extension,
                status=FileStatus(status),
                random_id=file_id,
                main_ext=file_extension
            )
            
            return masked_image, response
            
        except Exception as e:
            app_logger.log_error(f"Error in OCR processing for {filename}", e)
            response = OCRResponse(
                filename=filename,
                img_type=file_extension,
                status=FileStatus.INTERNAL_ERROR,
                random_id=file_id,
                main_ext=file_extension
            )
            return image_array, response
    
    def _ocr_cv2(self, card_img: np.ndarray, det: List[List[float]]) -> Tuple[np.ndarray, str]:
        """Main OCR processing function - adapted from original code"""
        # Extract card regions (class_id = 0)
        card_lst = [i1[0:4] for i1 in det if int(i1[-1]) == 0]
        
        cnt = 1
        overall_status = "UNMASKED"
        
        for card in card_lst:
            app_logger.log_debug(f"Processing CARD NUMBER: {cnt}")
            
            # Extract card coordinates
            card_coords_int = (int(card[0]), int(card[1]), int(card[2]), int(card[3]))
            
            # Crop card region
            cropped_card_img = card_img[int(card[1]):int(card[3]), int(card[0]):int(card[2])]
            cropped_card_img_array = np.asarray(cropped_card_img)
            
            # Run OCR on cropped card
            result = self.ocr.ocr(cropped_card_img_array, cls=True)
            
            # Find card numbers in OCR result
            mask_card_number_area = self._find_card_number(result)
            
            if len(mask_card_number_area) > 0:
                overall_status = "MASKED"
                # Mask the card numbers
                masked_cropped_card = self._mask_card_cv2(cropped_card_img, mask_card_number_area)
                # Put the masked card back into the original image
                card_img[card_coords_int[1]:card_coords_int[3], card_coords_int[0]:card_coords_int[2]] = masked_cropped_card
            
            cnt += 1
        
        return card_img, overall_status
    
    def _find_card_number(self, ocr_result: List) -> List[Tuple[int, int, int, int]]:
        """Find card numbers in OCR result - adapted from original code"""
        mask_card_number_area = []
        
        for idx in range(len(ocr_result)):
            res = ocr_result[idx]
            if res is not None:
                for line in res:
                    # Extract text coordinates and data
                    text_coords = (
                        int(line[0][0][0]), int(line[0][0][1]), 
                        int(line[0][2][0]), int(line[0][2][1])
                    )
                    text_data = line[1][0]
                    
                    # Clean the text
                    text_data_cleaned = self._clean_text(text_data)
                    
                    # Handle date patterns (MM/YY format)
                    if '/' in text_data_cleaned:
                        text_data_cleaned_split = text_data_cleaned.split('/')
                        # Check if it's a card validity date
                        if (len(text_data_cleaned_split) == 2 and 
                            text_data_cleaned_split[0].isdigit() and 
                            text_data_cleaned_split[1].isdigit()):
                            if (len(text_data_cleaned_split[0]) == 2 and 
                                (len(text_data_cleaned_split[1]) == 2 or len(text_data_cleaned_split[1]) == 4)):
                                # This is a date, skip it
                                continue
                            else:
                                # Remove the slash for further processing
                                text_data_cleaned = text_data_cleaned.replace("/", "")
                    
                    # Check if it's a card number (digits only, length > 3)
                    if text_data_cleaned.isdigit() and len(text_data_cleaned) > 3:
                        mask_card_number_area.append(text_coords)
        
        return mask_card_number_area
    
    def _clean_text(self, text_data: str) -> str:
        """Clean text by removing special characters - from original code"""
        remove_special_characters = [',', ':', ';', '*', '_', ')', '(', '#', '-', ' ', '.']
        for char in remove_special_characters:
            text_data = text_data.replace(char, '')
        return text_data
    
    def _mask_card_cv2(self, cropped_card: np.ndarray, mask_card_number_area: List[Tuple[int, int, int, int]]) -> np.ndarray:
        """Mask card numbers with red rectangles - from original code"""
        for coords in mask_card_number_area:
            start_point = [coords[0], coords[1]]
            end_point = [coords[2], coords[3]]
            # Draw red rectangle to mask the card number
            cropped_card = cv2.rectangle(
                cropped_card, 
                start_point, 
                end_point, 
                color=(0, 0, 255),  # Red color in BGR
                thickness=-1  # Fill the rectangle
            )
        return cropped_card
