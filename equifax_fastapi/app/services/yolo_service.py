import os
import sys
from pathlib import Path
import torch
import torch.backends.cudnn as cudnn
import numpy as np
import cv2
from typing import List, Tuple, Optional

# Add YOLOv5 to path (assuming we copy the yolo files)
FILE = Path(__file__).resolve()
ROOT = FILE.parents[3] / "gituploadequifax" / "yolo_redactor"  # Path to original yolo_redactor
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from models.common import DetectMultiBackend
from utils.dataloaders import LoadImages
from utils.general import (check_img_size, non_max_suppression, scale_coords)
from utils.torch_utils import select_device

from ..core.config import config
from ..core.logger import app_logger
from ..models.schemas import Detection, YoloResponse, FileStatus

class YoloService:
    """YOLO model service for card detection"""
    
    def __init__(self):
        self.model = None
        self.device = None
        self.stride = None
        self.names = None
        self.pt = None
        self.imgsz = None
        self._load_model()
    
    def _load_model(self):
        """Load YOLO model with configuration"""
        try:
            # Set CUDA device
            os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Force CPU for now
            
            # Model parameters from config
            weights = config.yolo_weights
            data = config.yolo_data
            device = config.device
            half = False  # FP16 inference
            dnn = False   # OpenCV DNN
            
            # Load model
            self.device = select_device(device)
            self.model = DetectMultiBackend(weights, device=self.device, dnn=dnn, data=data, fp16=half)
            self.stride, self.names, self.pt = self.model.stride, self.model.names, self.model.pt
            self.imgsz = check_img_size(config.yolo_imgsz, s=self.stride)
            
            # Warmup
            self.model.warmup(imgsz=(1 if self.pt else 1, 3, *self.imgsz))
            
            app_logger.log_info("YOLO model loaded successfully")
            
        except Exception as e:
            app_logger.log_error("Failed to load YOLO model", e)
            raise
    
    def detect_cards(self, image_array: np.ndarray, filename: str, file_id: str, 
                    file_extension: str) -> YoloResponse:
        """Detect cards in image using YOLO"""
        try:
            # Convert numpy array to format expected by YOLO
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                # RGB to BGR for OpenCV
                image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            else:
                image_bgr = image_array
            
            # Prepare image for inference
            im = torch.from_numpy(image_bgr).to(self.device)
            im = im.half() if self.model.fp16 else im.float()
            im /= 255  # 0 - 255 to 0.0 - 1.0
            if len(im.shape) == 3:
                im = im[None]  # expand for batch dim
            
            # Inference
            pred = self.model(im, augment=False, visualize=False)
            
            # NMS
            pred = non_max_suppression(
                pred, 
                config.conf_thres, 
                config.iou_thres, 
                classes=None, 
                agnostic=False, 
                max_det=config.max_det
            )
            
            # Process predictions
            return self._process_predictions(pred, image_bgr, filename, file_id, file_extension)
            
        except Exception as e:
            app_logger.log_error(f"Error in YOLO detection for {filename}", e)
            return YoloResponse(
                filename=filename,
                img_type=file_extension,
                status=FileStatus.INTERNAL_ERROR,
                random_id=file_id,
                main_ext=file_extension,
                detections=[],
                activation_status=0
            )
    
    def _process_predictions(self, pred: List[torch.Tensor], image: np.ndarray, 
                           filename: str, file_id: str, file_extension: str) -> YoloResponse:
        """Process YOLO predictions and apply threshold logic"""
        
        detections = []
        cards = []
        card_numbers = []
        no_cards = []
        
        for det in pred:
            if len(det):
                # Rescale boxes from img_size to original image size
                det[:, :4] = scale_coords(image.shape[:2], det[:, :4], image.shape).round()
                
                # Process each detection
                for *xyxy, conf, cls in det:
                    class_id = int(cls)
                    confidence = float(conf)
                    class_name = self.names[class_id]
                    
                    detection = Detection(
                        x1=float(xyxy[0]),
                        y1=float(xyxy[1]),
                        x2=float(xyxy[2]),
                        y2=float(xyxy[3]),
                        confidence=confidence,
                        class_id=class_id,
                        class_name=class_name
                    )
                    detections.append(detection)
                    
                    # Apply threshold logic from original code
                    if class_id == 0 and confidence >= config.card_threshold:  # card
                        cards.append(detection)
                    elif class_id == 1 and confidence >= config.card_number_threshold:  # card_number
                        card_numbers.append(detection)
                    elif class_id == 2:  # no_card
                        no_cards.append(detection)
        
        # Sort by confidence (descending)
        cards = sorted(cards, key=lambda x: x.confidence, reverse=True)
        card_numbers = sorted(card_numbers, key=lambda x: x.confidence, reverse=True)
        no_cards = sorted(no_cards, key=lambda x: x.confidence, reverse=True)
        
        # Determine status and activation based on original logic
        if len(cards) == 0:
            status = FileStatus.NOT_A_CARD
            activation_status = 0
        else:
            # Check if we have card numbers detected
            card_number_detected = any(d.class_id == 1 for d in detections)
            
            if card_number_detected:
                status = FileStatus.MASKED
                activation_status = 0
            else:
                # Original logic: if more cards than card numbers, trigger OCR
                if len(cards) > len(card_numbers):
                    status = FileStatus.UNMASKED
                    activation_status = 1  # Trigger OCR
                else:
                    status = FileStatus.UNMASKED
                    activation_status = 0
        
        return YoloResponse(
            filename=filename,
            img_type=file_extension,
            status=status,
            random_id=file_id,
            main_ext=file_extension,
            detections=detections,
            activation_status=activation_status,
            dest_path=None  # We work in memory, no file path
        )
    
    def save_annotated_image(self, image_array: np.ndarray, detections: List[Detection]) -> np.ndarray:
        """Save image with detection annotations (for debugging/visualization)"""
        annotated_image = image_array.copy()
        
        for detection in detections:
            # Draw bounding box
            cv2.rectangle(
                annotated_image,
                (int(detection.x1), int(detection.y1)),
                (int(detection.x2), int(detection.y2)),
                (0, 255, 0),  # Green color
                2
            )
            
            # Add label
            label = f"{detection.class_name}: {detection.confidence:.2f}"
            cv2.putText(
                annotated_image,
                label,
                (int(detection.x1), int(detection.y1) - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (0, 255, 0),
                1
            )
        
        return annotated_image
