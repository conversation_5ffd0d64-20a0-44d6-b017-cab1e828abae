import os
import uuid
import base64
import shutil
from pathlib import Path
from typing import List, Tuple, Optional
import numpy as np
from PIL import Image
import cv2
from pdf2image import convert_from_path
import img2pdf
import docx2txt
import pillow_heif

from ..core.config import config
from ..core.logger import app_logger
from ..models.schemas import FileValidationResult, FileConversionResult, ImageArray

# Register HEIF opener
pillow_heif.register_heif_opener()

class FileHandler:
    """Handles file validation, conversion, and result stitching"""
    
    def __init__(self):
        self.valid_extensions = config.valid_extensions
        self.temp_dirs = config.temp_dirs
        self.output_dirs = config.output_dirs
        self.poppler_path = config.poppler_path
        
        # Create necessary directories
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories if they don't exist"""
        dirs_to_create = [
            self.temp_dirs.get('pdf', 'temp_pdf'),
            self.temp_dirs.get('docx', 'temp_docx'),
            self.temp_dirs.get('heic', 'temp_heic'),
            self.output_dirs.get('source', 'Card_Img'),
            self.output_dirs.get('dest', 'Save_Card_Img'),
            self.output_dirs.get('pdf_out', 'pdf_out'),
            self.output_dirs.get('heic_out', 'heic_out')
        ]
        
        for dir_path in dirs_to_create:
            Path(dir_path).mkdir(exist_ok=True)
    
    def validate_file_type(self, file_extension: str) -> FileValidationResult:
        """Validate if file type is supported"""
        if file_extension in self.valid_extensions:
            return FileValidationResult(
                is_valid=True,
                file_type=file_extension
            )
        else:
            return FileValidationResult(
                is_valid=False,
                file_type=file_extension,
                error_message=f"Unsupported file type: {file_extension}"
            )
    
    def convert_file_to_images(self, file_data: bytes, file_extension: str) -> FileConversionResult:
        """Convert various file formats to image arrays"""
        file_id = str(uuid.uuid1())
        
        try:
            if file_extension.lower() in ['pdf']:
                return self._convert_pdf_to_images(file_data, file_id, file_extension)
            elif file_extension.lower() in ['docx']:
                return self._convert_docx_to_images(file_data, file_id, file_extension)
            elif file_extension.lower() in ['heic']:
                return self._convert_heic_to_images(file_data, file_id, file_extension)
            elif file_extension.lower() in ['jpg', 'jpeg', 'png']:
                return self._convert_image_to_array(file_data, file_id, file_extension)
            else:
                return FileConversionResult(
                    success=False,
                    file_id=file_id,
                    original_extension=file_extension,
                    error_message=f"Unsupported file type: {file_extension}"
                )
        except Exception as e:
            app_logger.log_error(f"Error converting file {file_id}", e)
            return FileConversionResult(
                success=False,
                file_id=file_id,
                original_extension=file_extension,
                error_message=str(e)
            )
    
    def _convert_pdf_to_images(self, file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
        """Convert PDF to image arrays"""
        temp_dir = Path(self.temp_dirs['pdf']) / file_id
        temp_dir.mkdir(exist_ok=True)
        
        # Save PDF file
        pdf_path = temp_dir / f"{file_id}.{file_extension}"
        with open(pdf_path, 'wb') as f:
            f.write(file_data)
        
        # Convert PDF to images
        poppler_path_full = Path(self.poppler_path) if self.poppler_path else None
        images = convert_from_path(
            str(pdf_path), 
            200, 
            poppler_path=str(poppler_path_full) if poppler_path_full else None
        )
        
        # Save images and create arrays
        source_dir = Path(self.output_dirs['source']) / file_id
        source_dir.mkdir(exist_ok=True)
        
        image_arrays = []
        for num, image in enumerate(images):
            img_path = source_dir / f"page{num}_{file_id}.jpg"
            image.save(img_path, 'JPEG')
            
            # Convert to array
            img_array = np.array(image)
            image_arrays.append(ImageArray(
                data=img_array,
                width=img_array.shape[1],
                height=img_array.shape[0],
                channels=img_array.shape[2] if len(img_array.shape) > 2 else 1,
                page_number=num
            ))
        
        return FileConversionResult(
            success=True,
            images=image_arrays,
            file_id=file_id,
            original_extension=file_extension
        )
    
    def _convert_docx_to_images(self, file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
        """Convert DOCX to images"""
        temp_dir = Path(self.temp_dirs['docx']) / file_id
        temp_dir.mkdir(exist_ok=True)
        
        # Save DOCX file
        docx_path = temp_dir / f"{file_id}.{file_extension}"
        with open(docx_path, 'wb') as f:
            f.write(file_data)
        
        # Extract images from DOCX
        source_dir = Path(self.output_dirs['source']) / file_id
        source_dir.mkdir(exist_ok=True)
        
        docx2txt.process(str(docx_path), str(source_dir))
        
        # Process extracted images
        image_arrays = []
        for img_file in source_dir.glob('*'):
            if img_file.suffix.lower() in ['.jpg', '.jpeg', '.png']:
                image = Image.open(img_file)
                img_array = np.array(image)
                image_arrays.append(ImageArray(
                    data=img_array,
                    width=img_array.shape[1],
                    height=img_array.shape[0],
                    channels=img_array.shape[2] if len(img_array.shape) > 2 else 1
                ))
        
        return FileConversionResult(
            success=True,
            images=image_arrays,
            file_id=file_id,
            original_extension=file_extension
        )
    
    def _convert_heic_to_images(self, file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
        """Convert HEIC to image arrays"""
        temp_dir = Path(self.temp_dirs['heic']) / file_id
        temp_dir.mkdir(exist_ok=True)
        
        # Save HEIC file
        heic_path = temp_dir / f"{file_id}.{file_extension}"
        with open(heic_path, 'wb') as f:
            f.write(file_data)
        
        # Convert HEIC to image
        heif_file = pillow_heif.read_heif(str(heic_path))
        image = Image.frombytes(
            heif_file.mode,
            heif_file.size,
            bytes(heif_file.data),
            "raw"
        )
        
        # Save as PNG
        source_dir = Path(self.output_dirs['source']) / file_id
        source_dir.mkdir(exist_ok=True)
        png_path = source_dir / f"{file_id}.png"
        image.save(png_path)
        
        # Convert to array
        img_array = np.array(image)
        image_arrays = [ImageArray(
            data=img_array,
            width=img_array.shape[1],
            height=img_array.shape[0],
            channels=img_array.shape[2] if len(img_array.shape) > 2 else 1
        )]
        
        return FileConversionResult(
            success=True,
            images=image_arrays,
            file_id=file_id,
            original_extension=file_extension
        )
    
    def _convert_image_to_array(self, file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
        """Convert regular image to array"""
        # Save image file
        source_dir = Path(self.output_dirs['source']) / file_id
        source_dir.mkdir(exist_ok=True)
        
        img_path = source_dir / f"{file_id}.{file_extension}"
        with open(img_path, 'wb') as f:
            f.write(file_data)
        
        # Load and convert to array
        image = Image.open(img_path)
        img_array = np.array(image)
        
        image_arrays = [ImageArray(
            data=img_array,
            width=img_array.shape[1],
            height=img_array.shape[0],
            channels=img_array.shape[2] if len(img_array.shape) > 2 else 1
        )]
        
        return FileConversionResult(
            success=True,
            images=image_arrays,
            file_id=file_id,
            original_extension=file_extension
        )

    def combine_processed_images(self, file_id: str, original_extension: str) -> Tuple[bool, str]:
        """Combine processed images back to original format"""
        try:
            if original_extension.lower() == 'pdf':
                return self._combine_to_pdf(file_id, original_extension)
            elif original_extension.lower() == 'heic':
                return self._combine_to_heic(file_id, original_extension)
            else:
                # For regular images and DOCX, return the processed files as-is
                return self._get_processed_image(file_id, original_extension)
        except Exception as e:
            app_logger.log_error(f"Error combining processed images for {file_id}", e)
            return False, str(e)

    def _combine_to_pdf(self, file_id: str, original_extension: str) -> Tuple[bool, str]:
        """Combine processed images back to PDF"""
        source_dir = Path(self.output_dirs['dest']) / file_id
        pdf_files = sorted(source_dir.glob('*.jpg'))

        if not pdf_files:
            return False, "No processed images found"

        # Create output directory
        output_dir = Path(self.output_dirs['pdf_out']) / file_id
        output_dir.mkdir(exist_ok=True)

        # Convert images to PDF
        output_path = output_dir / f"{file_id}.pdf"
        with open(output_path, "wb") as f:
            f.write(img2pdf.convert([str(img) for img in pdf_files]))

        return True, str(output_path)

    def _combine_to_heic(self, file_id: str, original_extension: str) -> Tuple[bool, str]:
        """Convert processed image back to HEIC"""
        source_dir = Path(self.output_dirs['dest']) / file_id
        png_file = source_dir / f"{file_id}.png"

        if not png_file.exists():
            return False, "No processed image found"

        # Create output directory
        output_dir = Path(self.output_dirs['heic_out']) / file_id
        output_dir.mkdir(exist_ok=True)

        # Convert PNG to HEIC
        image = Image.open(png_file)
        output_path = output_dir / f"{file_id}.heic"
        image.save(output_path)

        return True, str(output_path)

    def _get_processed_image(self, file_id: str, original_extension: str) -> Tuple[bool, str]:
        """Get processed image file"""
        source_dir = Path(self.output_dirs['dest']) / file_id
        img_file = source_dir / f"{file_id}.{original_extension}"

        if img_file.exists():
            return True, str(img_file)
        else:
            return False, "Processed image not found"

    def clear_temp_files(self, file_id: str, original_extension: str):
        """Clear temporary files after processing"""
        try:
            dirs_to_clear = []

            if original_extension.lower() == 'pdf':
                dirs_to_clear.extend([
                    Path(self.temp_dirs['pdf']) / file_id,
                    Path(self.output_dirs['pdf_out']) / file_id
                ])
            elif original_extension.lower() == 'heic':
                dirs_to_clear.extend([
                    Path(self.temp_dirs['heic']) / file_id,
                    Path(self.output_dirs['heic_out']) / file_id
                ])
            elif original_extension.lower() == 'docx':
                dirs_to_clear.append(Path(self.temp_dirs['docx']) / file_id)

            # Always clear source and dest directories
            dirs_to_clear.extend([
                Path(self.output_dirs['source']) / file_id,
                Path(self.output_dirs['dest']) / file_id
            ])

            for dir_path in dirs_to_clear:
                if dir_path.exists():
                    shutil.rmtree(dir_path)

        except Exception as e:
            app_logger.log_error(f"Error clearing temp files for {file_id}", e)
