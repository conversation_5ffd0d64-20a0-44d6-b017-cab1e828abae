import base64
import uuid
from typing import Union, List
import numpy as np
from PIL import Image
import io

def generate_file_id() -> str:
    """Generate a unique file ID"""
    return str(uuid.uuid1())

def encode_image_to_base64(image_array: np.ndarray, format: str = 'JPEG') -> str:
    """Convert numpy image array to base64 string"""
    if image_array.dtype != np.uint8:
        image_array = (image_array * 255).astype(np.uint8)
    
    pil_image = Image.fromarray(image_array)
    img_buffer = io.BytesIO()
    pil_image.save(img_buffer, format=format)
    img_bytes = img_buffer.getvalue()
    
    return base64.b64encode(img_bytes).decode('utf-8')

def decode_base64_to_image(base64_string: str) -> np.ndarray:
    """Convert base64 string to numpy image array"""
    img_bytes = base64.b64decode(base64_string)
    pil_image = Image.open(io.BytesIO(img_bytes))
    return np.array(pil_image)

def validate_image_format(image_array: np.ndarray) -> bool:
    """Validate if image array has correct format"""
    if not isinstance(image_array, np.ndarray):
        return False
    
    if len(image_array.shape) not in [2, 3]:
        return False
    
    if len(image_array.shape) == 3 and image_array.shape[2] not in [1, 3, 4]:
        return False
    
    return True

def normalize_image_array(image_array: np.ndarray) -> np.ndarray:
    """Normalize image array to uint8 format"""
    if image_array.dtype == np.uint8:
        return image_array
    
    if image_array.max() <= 1.0:
        # Assume it's normalized to [0, 1]
        return (image_array * 255).astype(np.uint8)
    else:
        # Assume it's in [0, 255] but wrong dtype
        return image_array.astype(np.uint8)

def get_file_extension(filename: str) -> str:
    """Extract file extension from filename"""
    return filename.split('.')[-1].lower() if '.' in filename else ''

def is_supported_format(file_extension: str) -> bool:
    """Check if file format is supported"""
    supported_formats = ['jpg', 'jpeg', 'png', 'pdf', 'docx', 'heic']
    return file_extension.lower() in supported_formats

def create_error_response(message: str, status: str = "error") -> dict:
    """Create standardized error response"""
    return {
        "status": status,
        "message": message,
        "success": False
    }

def create_success_response(data: dict, message: str = "success") -> dict:
    """Create standardized success response"""
    return {
        "status": "success",
        "message": message,
        "success": True,
        "data": data
    }
