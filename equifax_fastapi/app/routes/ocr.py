from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import numpy as np
from typing import Dict, Any, List
import io
import base64
import json

from ..services.ocr_service import OCRService
from ..services.file_handler import FileHandler
from ..core.logger import app_logger
from ..models.schemas import ProcessFileRequest, ProcessFileResponse, FileStatus

router = APIRouter()

# Initialize services
ocr_service = OCRService()
file_handler = FileHandler()

@router.post("/ocr", response_model=Dict[str, Any])
async def ocr_processing(
    file_data: str,
    file_type: str,
    filename: str,
    random_id: str,
    main_ext: str,
    card_coordinates: str
):
    """
    OCR processing endpoint
    Processes image with card coordinates to detect and mask card numbers
    """
    try:
        # Parse card coordinates
        try:
            coordinates = json.loads(card_coordinates)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid card_coordinates format")
        
        # Decode base64 file data
        try:
            file_bytes = base64.b64decode(file_data)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid base64 file data")
        
        # Convert file to image arrays
        conversion_result = file_handler.convert_file_to_images(file_bytes, file_type)
        if not conversion_result.success:
            app_logger.log_api_call(
                filename=filename,
                file_type=file_type,
                is_card="Yes",
                status="Internal error",
                api="OCR"
            )
            return {
                "filename": filename,
                "img-type": file_type,
                "STATUS": "Internal error",
                "random_id": random_id,
                "main_ext": main_ext
            }
        
        # Process the first image (or main image) with OCR
        if not conversion_result.images:
            raise HTTPException(status_code=400, detail="No images found in file")
        
        image_array = conversion_result.images[0].data  # Process first/main image
        
        # Run OCR processing
        masked_image, ocr_response = ocr_service.process_image_with_ocr(
            image_array,
            coordinates,
            filename,
            random_id,
            file_type
        )
        
        # Log the OCR result
        app_logger.log_api_call(
            filename=filename,
            file_type=file_type,
            is_card="Yes",
            status=ocr_response.status.value,
            api="OCR"
        )
        
        # For multi-page documents, update the processed image
        processed_images = []
        for i, img_array in enumerate(conversion_result.images):
            if i == 0:
                processed_images.append(masked_image)
            else:
                processed_images.append(img_array.data)
        
        # Combine processed images back to original format
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, file_type
        )
        
        if not success:
            app_logger.log_error(f"Error combining OCR processed images: {result_data}")
            return {
                "filename": filename,
                "img-type": file_type,
                "STATUS": "Internal error",
                "random_id": random_id,
                "main_ext": main_ext
            }
        
        # Return response in original format
        return {
            "filename": filename,
            "img-type": file_type,
            "STATUS": ocr_response.status.value,
            "random_id": random_id,
            "main_ext": main_ext
        }
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.log_error(f"Error in OCR processing endpoint", e)
        app_logger.log_api_call(
            filename=filename if 'filename' in locals() else "unknown",
            file_type=file_type if 'file_type' in locals() else "unknown",
            is_card="Yes",
            status="Internal error",
            api="OCR"
        )
        return {
            "filename": filename if 'filename' in locals() else "unknown",
            "img-type": file_type if 'file_type' in locals() else "unknown",
            "STATUS": "Internal error",
            "random_id": random_id if 'random_id' in locals() else "unknown",
            "main_ext": main_ext if 'main_ext' in locals() else "unknown"
        }


@router.post("/ocr/stream")
async def ocr_processing_stream(
    file_data: str,
    file_type: str,
    filename: str,
    random_id: str,
    main_ext: str,
    card_coordinates: str
):
    """
    OCR processing endpoint with streaming response
    Returns processed file as streaming response
    """
    try:
        # Parse card coordinates
        coordinates = json.loads(card_coordinates)
        
        # Decode base64 file data
        file_bytes = base64.b64decode(file_data)
        
        # Convert file to image arrays
        conversion_result = file_handler.convert_file_to_images(file_bytes, file_type)
        if not conversion_result.success:
            raise HTTPException(status_code=400, detail=conversion_result.error_message)
        
        if not conversion_result.images:
            raise HTTPException(status_code=400, detail="No images found in file")
        
        # Process images with OCR
        processed_images = []
        for i, image_array in enumerate(conversion_result.images):
            if i == 0:  # Process first image with OCR
                masked_image, _ = ocr_service.process_image_with_ocr(
                    image_array.data,
                    coordinates,
                    filename,
                    random_id,
                    file_type
                )
                processed_images.append(masked_image)
            else:
                processed_images.append(image_array.data)
        
        # Combine and return as stream
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, file_type
        )
        
        if not success:
            raise HTTPException(status_code=500, detail=f"Error processing: {result_data}")
        
        # Create streaming response
        if isinstance(result_data, bytes):
            return StreamingResponse(
                io.BytesIO(result_data),
                media_type=content_type,
                headers={"Content-Disposition": f"attachment; filename=processed_{filename}.{file_type}"}
            )
        else:
            raise HTTPException(status_code=500, detail="Unexpected result format")
            
    except HTTPException:
        raise
    except Exception as e:
        app_logger.log_error(f"Error in OCR streaming endpoint", e)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ocr/process", response_model=ProcessFileResponse)
async def ocr_process_file(request: ProcessFileRequest, card_coordinates: List[List[float]]):
    """
    Simplified OCR processing endpoint using Pydantic models
    """
    try:
        # Decode base64 file data
        file_data = base64.b64decode(request.file_data)
        
        # Convert file to image arrays
        conversion_result = file_handler.convert_file_to_images(file_data, request.file_type)
        if not conversion_result.success:
            return ProcessFileResponse(
                img_str=request.file_data,
                img_type=request.file_type,
                status=FileStatus.INTERNAL_ERROR
            )
        
        # Process with OCR
        processed_images = []
        overall_status = FileStatus.UNMASKED
        
        for image_array in conversion_result.images:
            masked_image, ocr_response = ocr_service.process_image_with_ocr(
                image_array.data,
                card_coordinates,
                conversion_result.file_id,
                conversion_result.file_id,
                request.file_type
            )
            processed_images.append(masked_image)
            
            if ocr_response.status == FileStatus.MASKED:
                overall_status = FileStatus.MASKED
        
        # Combine processed images
        success, result_data, _ = file_handler.combine_processed_images_to_bytes(
            processed_images, request.file_type
        )
        
        if not success:
            return ProcessFileResponse(
                img_str=request.file_data,
                img_type=request.file_type,
                status=FileStatus.INTERNAL_ERROR
            )
        
        # Encode result
        if isinstance(result_data, list):
            result_base64 = [base64.b64encode(img_bytes).decode('utf-8') for img_bytes in result_data]
        else:
            result_base64 = base64.b64encode(result_data).decode('utf-8')
        
        return ProcessFileResponse(
            img_str=result_base64,
            img_type=request.file_type,
            status=overall_status
        )
        
    except Exception as e:
        app_logger.log_error(f"Error in OCR process endpoint", e)
        return ProcessFileResponse(
            img_str=request.file_data,
            img_type=request.file_type,
            status=FileStatus.INTERNAL_ERROR
        )
