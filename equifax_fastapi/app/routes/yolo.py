from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import numpy as np
from typing import Dict, Any
import io
import base64

from ..services.yolo_service import YoloService
from ..services.file_handler import FileHandler
from ..core.logger import app_logger
from ..models.schemas import ProcessFileRequest, ProcessFileResponse, FileStatus

router = APIRouter()

# Initialize services
yolo_service = YoloService()
file_handler = FileHandler()

@router.post("/yolo", response_model=Dict[str, Any])
async def yolo_detection(request: ProcessFileRequest):
    """
    YOLO detection endpoint
    Processes uploaded file and detects cards using YOLO model
    """
    try:
        # Decode base64 file data
        file_data = base64.b64decode(request.file_data)
        
        # Validate file type
        validation_result = file_handler.validate_file_type(request.file_type)
        if not validation_result.is_valid:
            app_logger.log_api_call(
                filename="Invalid file",
                file_type=request.file_type,
                is_card="No",
                status="File read error",
                api="File read error"
            )
            return {
                "img-str": request.file_data,
                "img-type": request.file_type,
                "status": "File read error"
            }
        
        # Convert file to image arrays
        conversion_result = file_handler.convert_file_to_images(file_data, request.file_type)
        if not conversion_result.success:
            app_logger.log_api_call(
                filename=conversion_result.file_id,
                file_type=request.file_type,
                is_card="No",
                status="File read error",
                api="File read error"
            )
            return {
                "img-str": request.file_data,
                "img-type": request.file_type,
                "status": "File read error"
            }
        
        # Process each image with YOLO
        processed_images = []
        overall_status = FileStatus.UNMASKED
        activation_status = 0
        detections_for_ocr = []
        
        for page_num, image_array in enumerate(conversion_result.images):
            # Run YOLO detection
            yolo_response = yolo_service.detect_cards(
                image_array.data,
                conversion_result.file_id,
                conversion_result.file_id,
                request.file_type
            )
            
            # Log the detection result
            app_logger.log_api_call(
                filename=conversion_result.file_id,
                file_type=request.file_type,
                is_card="Yes" if yolo_response.status != FileStatus.NOT_A_CARD else "No",
                status=yolo_response.status.value,
                api="yolo",
                page_no=page_num if len(conversion_result.images) > 1 else None
            )
            
            # Update overall status
            if yolo_response.status == FileStatus.MASKED:
                overall_status = FileStatus.MASKED
            elif yolo_response.status == FileStatus.NOT_A_CARD and overall_status == FileStatus.UNMASKED:
                overall_status = FileStatus.NOT_A_CARD
            
            # Check if OCR should be triggered
            if yolo_response.activation_status == 1:
                activation_status = 1
                # Get coordinates for OCR
                ocr_coords = yolo_service.get_detection_coordinates_for_ocr(yolo_response.detections)
                detections_for_ocr.append({
                    'image_index': page_num,
                    'coordinates': ocr_coords,
                    'image_data': image_array.data
                })
            
            # For visualization, save annotated image
            if yolo_response.detections:
                annotated_image = yolo_service.save_annotated_image(image_array.data, yolo_response.detections)
                processed_images.append(annotated_image)
            else:
                processed_images.append(image_array.data)
        
        # Combine processed images back to original format
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, request.file_type
        )
        
        if not success:
            raise HTTPException(status_code=500, detail=f"Error combining images: {result_data}")
        
        # Encode result as base64
        if isinstance(result_data, list):
            # For DOCX with multiple images
            result_base64 = [base64.b64encode(img_bytes).decode('utf-8') for img_bytes in result_data]
        else:
            result_base64 = base64.b64encode(result_data).decode('utf-8')
        
        # Prepare response
        response_data = {
            "filename": conversion_result.file_id,
            "img-type": request.file_type,
            "STATUS": overall_status.value,
            "random_id": conversion_result.file_id,
            "main_ext": request.file_type,
            "activationstatus": activation_status
        }
        
        # Add OCR data if needed
        if activation_status == 1 and detections_for_ocr:
            response_data["det_ocr"] = detections_for_ocr[0]["coordinates"]  # For first image
            response_data["dest"] = "memory"  # Indicate we work in memory
        
        return response_data
        
    except Exception as e:
        app_logger.log_error(f"Error in YOLO detection endpoint", e)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/yolo/stream")
async def yolo_detection_stream(request: ProcessFileRequest):
    """
    YOLO detection endpoint with streaming response
    Returns processed file as streaming response
    """
    try:
        # Decode base64 file data
        file_data = base64.b64decode(request.file_data)
        
        # Validate and convert file
        validation_result = file_handler.validate_file_type(request.file_type)
        if not validation_result.is_valid:
            raise HTTPException(status_code=400, detail=validation_result.error_message)
        
        conversion_result = file_handler.convert_file_to_images(file_data, request.file_type)
        if not conversion_result.success:
            raise HTTPException(status_code=400, detail=conversion_result.error_message)
        
        # Process with YOLO
        processed_images = []
        for image_array in conversion_result.images:
            yolo_response = yolo_service.detect_cards(
                image_array.data,
                conversion_result.file_id,
                conversion_result.file_id,
                request.file_type
            )
            
            if yolo_response.detections:
                annotated_image = yolo_service.save_annotated_image(image_array.data, yolo_response.detections)
                processed_images.append(annotated_image)
            else:
                processed_images.append(image_array.data)
        
        # Combine and return as stream
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, request.file_type
        )
        
        if not success:
            raise HTTPException(status_code=500, detail=f"Error processing: {result_data}")
        
        # Create streaming response
        if isinstance(result_data, bytes):
            return StreamingResponse(
                io.BytesIO(result_data),
                media_type=content_type,
                headers={"Content-Disposition": f"attachment; filename=processed.{request.file_type}"}
            )
        else:
            raise HTTPException(status_code=500, detail="Unexpected result format")
            
    except Exception as e:
        app_logger.log_error(f"Error in YOLO streaming endpoint", e)
        raise HTTPException(status_code=500, detail=str(e))
