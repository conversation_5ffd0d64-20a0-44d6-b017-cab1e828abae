debug: true

yolo:
  weights: "runs/train/exp/weights/best.pt"
  data: "card_data/card.yaml"
  card_threshold: 0.6
  card_number_threshold: 0.4
  imgsz: [640, 640]
  conf_thres: 0.08
  iou_thres: 0.10
  max_det: 1000
  device: ""  # cuda device, i.e. 0 or 0,1,2,3 or cpu

ocr:
  use_angle_cls: true
  lang: "en"
  show_log: false

logging:
  log_dir: "logs"
  log_file: "app.log"
  level: "INFO"
  format: "%(message)s"

file_handling:
  valid_extensions: ["JPEG", "JPG", "PNG", "PDF", "jpg", "jpeg", "png", "pdf", "docx", "DOCX", "heic", "HEIC"]
  temp_dirs:
    pdf: "temp_pdf"
    docx: "temp_docx" 
    heic: "temp_heic"
  output_dirs:
    source: "Card_Img"
    dest: "Save_Card_Img"
    pdf_out: "pdf_out"
    heic_out: "heic_out"
  poppler_path: "poppler-0.68.0/bin"

api:
  host: "127.0.0.1"
  port: 8000
  timeout: 600
